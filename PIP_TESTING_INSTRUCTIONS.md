# PiP Testing Instructions for iOS

## Current Status
I've implemented comprehensive logging for the Picture-in-Picture functionality. The logs will help us identify where the issue is occurring.

## Testing Steps

### 1. Rebuild the iOS Project
Since we added new native modules, you need to rebuild the iOS project:

```bash
# Clean and rebuild
cd ios
rm -rf build
cd ..
npx expo run:ios --clear
```

### 2. Check Console Logs
When you run the app, look for these log messages in the console:

**PiP Service Initialization:**
```
[PiP Service] Platform: ios
[PiP Service] PipModule available: true/false
[PiP Service] iOS PipModule methods: [list of methods]
[PiP Service] All NativeModules: [list of all modules]
```

**Hook Initialization:**
```
[PiP Hook] usePictureInPicture hook initialized
[PiP Hook] PiP supported: true/false
```

**Provider Call Component:**
```
[Provider Call] PiP state changed - isInPip: false isPipSupported: true/false
```

### 3. Test PiP Debug Screen
Navigate to the debug screen by adding `/pip-debug` to your app URL or create a navigation button to test:

```typescript
// Add this to any screen for testing
import { useRouter } from 'expo-router';

const router = useRouter();
// Then use: router.push('/pip-debug');
```

### 4. Test in Provider Call
1. Start a provider video call
2. Look for the minimize button (⬇️) in the call controls
3. Tap the button and check console logs

## Expected Log Flow

### If Working Correctly:
1. `[PiP Service] PipModule available: true`
2. `[PiP Hook] PiP supported: true`
3. `[Provider Call] handleEnterPip called`
4. `[PiP Service] Attempting iOS PiP entry`
5. `[PiP Service] iOS PiP entry result: true`

### If Native Module Not Found:
1. `[PiP Service] PipModule available: false`
2. `[PiP Hook] PiP supported: false`
3. PiP button won't appear in UI

### If iOS PiP Not Supported:
1. `[PiP Service] PipModule available: true`
2. `[PiP Service] iOS device PiP support check result: false`
3. Button appears but fails when pressed

## Troubleshooting

### Issue 1: Native Module Not Found
**Symptoms:** `PipModule available: false`
**Solution:** 
1. Check if PipModule.h and PipModule.m are in Xcode project
2. Rebuild iOS project completely
3. Check iOS build logs for compilation errors

### Issue 2: PiP Not Supported on Device
**Symptoms:** `iOS device PiP support check result: false`
**Solution:**
- Test on iOS 14.0+ device/simulator
- Check if device supports PiP (some older devices don't)

### Issue 3: PiP Fails to Start
**Symptoms:** `iOS PiP entry result: false` or error messages
**Solution:**
- Check iOS permissions
- Ensure app has proper background modes
- Check if another app is using PiP

## Manual Testing Commands

Add these to any component for manual testing:

```typescript
import { NativeModules } from 'react-native';
const { PipModule } = NativeModules;

// Test 1: Check module availability
console.log('PipModule available:', !!PipModule);
console.log('PipModule methods:', PipModule ? Object.keys(PipModule) : 'N/A');

// Test 2: Check device support
if (PipModule && PipModule.isPictureInPictureSupported) {
  PipModule.isPictureInPictureSupported()
    .then(supported => console.log('Device supports PiP:', supported))
    .catch(error => console.log('PiP support check error:', error));
}

// Test 3: Try direct PiP call
if (PipModule && PipModule.enterPictureInPicture) {
  PipModule.enterPictureInPicture()
    .then(result => console.log('Direct PiP result:', result))
    .catch(error => console.log('Direct PiP error:', error));
}
```

## Next Steps Based on Results

1. **If native module not found:** We need to ensure it's properly linked in Xcode
2. **If PiP not supported:** We need to check device/iOS version compatibility
3. **If PiP fails:** We need to debug the native implementation
4. **If everything works:** We can optimize the integration with Zoom Video SDK

Please run these tests and share the console output so we can identify the exact issue!
