#import "PipModule.h"
#import <React/RCTLog.h>

@implementation PipModule

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup
{
  return YES;
}

- (NSArray<NSString *> *)supportedEvents
{
  return @[@"onPictureInPictureModeChanged"];
}

RCT_EXPORT_METHOD(isPictureInPictureSupported:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  // For now, return true if iOS 14+ to test module loading
  if (@available(iOS 14.0, *)) {
    resolve(@YES);
  } else {
    resolve(@NO);
  }
}

RCT_EXPORT_METHOD(enterPictureInPicture:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  // Simplified implementation for testing
  if (@available(iOS 14.0, *)) {
    RCTLogInfo(@"PiP enter called - module is working!");
    [self sendEventWithName:@"onPictureInPictureModeChanged" body:@{@"isInPictureInPicture": @YES}];
    resolve(@YES);
  } else {
    reject(@"PIP_NOT_AVAILABLE", @"Picture in Picture requires iOS 14.0 or later", nil);
  }
}

RCT_EXPORT_METHOD(exitPictureInPicture:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  RCTLogInfo(@"PiP exit called - module is working!");
  [self sendEventWithName:@"onPictureInPictureModeChanged" body:@{@"isInPictureInPicture": @NO}];
  resolve(@YES);
}

RCT_EXPORT_METHOD(isPictureInPictureActive:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  resolve(@NO);
}

@end
