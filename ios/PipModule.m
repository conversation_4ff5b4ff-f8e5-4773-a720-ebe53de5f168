#import "PipModule.h"
#import <React/RCTLog.h>

@implementation PipModule

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup
{
  return YES;
}

- (NSArray<NSString *> *)supportedEvents
{
  return @[@"onPictureInPictureModeChanged"];
}

RCT_EXPORT_METHOD(isPictureInPictureSupported:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  BOOL isSupported = [AVPictureInPictureController isPictureInPictureSupported];
  resolve(@(isSupported));
}

RCT_EXPORT_METHOD(enterPictureInPicture:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    if (@available(iOS 14.0, *)) {
      if ([AVPictureInPictureController isPictureInPictureSupported]) {
        // For video calls, we need to create a dummy player layer
        // In a real implementation, this would be connected to the actual video stream
        AVPlayer *player = [[AVPlayer alloc] init];
        AVPlayerLayer *playerLayer = [AVPlayerLayer playerLayerWithPlayer:player];
        
        self.playerLayer = playerLayer;
        self.pipController = [[AVPictureInPictureController alloc] initWithPlayerLayer:playerLayer];
        self.pipController.delegate = self;
        
        [self.pipController startPictureInPicture];
        resolve(@YES);
      } else {
        reject(@"PIP_NOT_SUPPORTED", @"Picture in Picture is not supported on this device", nil);
      }
    } else {
      reject(@"PIP_NOT_AVAILABLE", @"Picture in Picture requires iOS 14.0 or later", nil);
    }
  });
}

RCT_EXPORT_METHOD(exitPictureInPicture:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    if (self.pipController && self.pipController.isPictureInPictureActive) {
      [self.pipController stopPictureInPicture];
      resolve(@YES);
    } else {
      resolve(@NO);
    }
  });
}

RCT_EXPORT_METHOD(isPictureInPictureActive:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  BOOL isActive = self.pipController ? self.pipController.isPictureInPictureActive : NO;
  resolve(@(isActive));
}

#pragma mark - AVPictureInPictureControllerDelegate

- (void)pictureInPictureControllerWillStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
{
  RCTLogInfo(@"PiP will start");
}

- (void)pictureInPictureControllerDidStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
{
  RCTLogInfo(@"PiP did start");
  [self sendEventWithName:@"onPictureInPictureModeChanged" body:@{@"isInPictureInPicture": @YES}];
}

- (void)pictureInPictureControllerWillStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
{
  RCTLogInfo(@"PiP will stop");
}

- (void)pictureInPictureControllerDidStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
{
  RCTLogInfo(@"PiP did stop");
  [self sendEventWithName:@"onPictureInPictureModeChanged" body:@{@"isInPictureInPicture": @NO}];
}

- (void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController
           failedToStartPictureInPictureWithError:(NSError *)error
{
  RCTLogError(@"PiP failed to start: %@", error.localizedDescription);
  [self sendEventWithName:@"onPictureInPictureModeChanged" body:@{@"isInPictureInPicture": @NO, @"error": error.localizedDescription}];
}

@end
