const { withAndroidManifest } = require('@expo/config-plugins');

const withAndroidPiP = (config) => {
  return withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // Find the main activity
    const mainActivity = androidManifest.manifest.application[0].activity.find(
      (activity) => activity.$['android:name'] === '.MainActivity'
    );
    
    if (mainActivity) {
      // Add PiP support to the main activity
      mainActivity.$['android:supportsPictureInPicture'] = 'true';
      mainActivity.$['android:resizeableActivity'] = 'true';
      
      // Add PiP configuration change handling
      if (!mainActivity.$['android:configChanges']) {
        mainActivity.$['android:configChanges'] = '';
      }
      
      const configChanges = mainActivity.$['android:configChanges'];
      const pipConfigChanges = ['screenSize', 'smallestScreenSize', 'screenLayout', 'orientation'];
      
      pipConfigChanges.forEach((change) => {
        if (!configChanges.includes(change)) {
          mainActivity.$['android:configChanges'] = configChanges 
            ? `${configChanges}|${change}` 
            : change;
        }
      });
    }
    
    return config;
  });
};

module.exports = withAndroidPiP;
