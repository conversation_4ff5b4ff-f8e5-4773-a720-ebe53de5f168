const { withAndroidManifest, withXcodeProject } = require('@expo/config-plugins');

const withPiPSupport = (config) => {
  // Android PiP configuration
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // Find the main activity
    const mainActivity = androidManifest.manifest.application[0].activity.find(
      (activity) => activity.$['android:name'] === '.MainActivity'
    );

    if (mainActivity) {
      // Add PiP support to the main activity
      mainActivity.$['android:supportsPictureInPicture'] = 'true';
      mainActivity.$['android:resizeableActivity'] = 'true';

      // Add PiP configuration change handling
      if (!mainActivity.$['android:configChanges']) {
        mainActivity.$['android:configChanges'] = '';
      }

      const configChanges = mainActivity.$['android:configChanges'];
      const pipConfigChanges = ['screenSize', 'smallestScreenSize', 'screenLayout', 'orientation'];

      pipConfigChanges.forEach((change) => {
        if (!configChanges.includes(change)) {
          mainActivity.$['android:configChanges'] = configChanges
            ? `${configChanges}|${change}`
            : change;
        }
      });
    }

    return config;
  });

  // iOS PiP configuration
  config = withXcodeProject(config, (config) => {
    const xcodeProject = config.modResults;

    // Add PipModule files to the project if they don't exist
    const pipModuleH = 'PipModule.h';
    const pipModuleM = 'PipModule.m';

    // Check if files are already added
    const existingH = xcodeProject.hasFile(pipModuleH);
    const existingM = xcodeProject.hasFile(pipModuleM);

    if (!existingH) {
      xcodeProject.addHeaderFile(pipModuleH);
    }

    if (!existingM) {
      xcodeProject.addSourceFile(pipModuleM);
    }

    return config;
  });

  return config;
};

module.exports = withPiPSupport;
