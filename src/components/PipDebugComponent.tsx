import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform, NativeModules } from 'react-native';
import { usePictureInPicture } from '../hooks/usePictureInPicture';

const { PipModule } = NativeModules;

const PipDebugComponent: React.FC = () => {
  const { isInPip, isPipSupported, enterPip, exitPip } = usePictureInPicture();
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugInfo(prev => [...prev.slice(-10), `${timestamp}: ${message}`]);
    console.log(`[PiP Debug] ${message}`);
  };

  useEffect(() => {
    addDebugLog(`Component mounted on ${Platform.OS}`);
    addDebugLog(`PipModule available: ${!!PipModule}`);
    addDebugLog(`isPipSupported: ${isPipSupported}`);
    addDebugLog(`isInPip: ${isInPip}`);
    
    if (Platform.OS === 'ios' && PipModule) {
      addDebugLog(`iOS PipModule methods: ${Object.keys(PipModule).join(', ')}`);
    }
    
    // Test iOS PiP support check
    if (Platform.OS === 'ios' && PipModule && PipModule.isPictureInPictureSupported) {
      PipModule.isPictureInPictureSupported()
        .then((supported: boolean) => {
          addDebugLog(`iOS device PiP support: ${supported}`);
        })
        .catch((error: any) => {
          addDebugLog(`iOS PiP support check error: ${error.message}`);
        });
    }
  }, []);

  useEffect(() => {
    addDebugLog(`PiP state changed - isInPip: ${isInPip}, isPipSupported: ${isPipSupported}`);
  }, [isInPip, isPipSupported]);

  const handleTestEnterPip = async () => {
    addDebugLog('Test Enter PiP button pressed');
    try {
      const result = await enterPip();
      addDebugLog(`Enter PiP result: ${result}`);
    } catch (error) {
      addDebugLog(`Enter PiP error: ${error}`);
    }
  };

  const handleTestExitPip = async () => {
    addDebugLog('Test Exit PiP button pressed');
    try {
      const result = await exitPip();
      addDebugLog(`Exit PiP result: ${result}`);
    } catch (error) {
      addDebugLog(`Exit PiP error: ${error}`);
    }
  };

  const handleDirectNativeCall = async () => {
    addDebugLog('Direct native call test');
    if (Platform.OS === 'ios' && PipModule) {
      try {
        if (PipModule.enterPictureInPicture) {
          addDebugLog('Calling PipModule.enterPictureInPicture directly');
          const result = await PipModule.enterPictureInPicture();
          addDebugLog(`Direct native call result: ${result}`);
        } else {
          addDebugLog('PipModule.enterPictureInPicture method not found');
        }
      } catch (error) {
        addDebugLog(`Direct native call error: ${error}`);
      }
    } else {
      addDebugLog('PipModule not available for direct call');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>PiP Debug Component</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>Platform: {Platform.OS}</Text>
        <Text style={styles.statusText}>PiP Supported: {isPipSupported ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>In PiP: {isInPip ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>Native Module: {PipModule ? 'Available' : 'Not Available'}</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, !isPipSupported && styles.disabledButton]} 
          onPress={handleTestEnterPip}
          disabled={!isPipSupported || isInPip}
        >
          <Text style={styles.buttonText}>Test Enter PiP</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, !isPipSupported && styles.disabledButton]} 
          onPress={handleTestExitPip}
          disabled={!isPipSupported || !isInPip}
        >
          <Text style={styles.buttonText}>Test Exit PiP</Text>
        </TouchableOpacity>

        {Platform.OS === 'ios' && (
          <TouchableOpacity 
            style={styles.button} 
            onPress={handleDirectNativeCall}
          >
            <Text style={styles.buttonText}>Direct Native Call</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.logContainer}>
        <Text style={styles.logTitle}>Debug Log:</Text>
        {debugInfo.map((log, index) => (
          <Text key={index} style={styles.logText}>{log}</Text>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusText: {
    fontSize: 16,
    marginBottom: 5,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  logContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    flex: 1,
  },
  logTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  logText: {
    fontSize: 12,
    marginBottom: 2,
    fontFamily: 'monospace',
  },
});

export default PipDebugComponent;
