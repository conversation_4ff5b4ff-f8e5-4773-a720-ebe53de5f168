import React, { useState, useEffect } from "react";
import { Star, Check } from "@tamagui/lucide-icons";
import { Text, View, XStack } from "tamagui";

type RatingModuleProps = {
  title: string;
  onRatingChange: (value: number) => void;
  onAutoSave?: (value: number) => Promise<void>;
  isEditable: boolean;
  rating: number;
  isSubmitting?: boolean;
  showSavedIndicator?: boolean;
  isSubmitted?: boolean;
};

const RatingModule = ({
  title,
  onRatingChange,
  onAutoSave,
  isEditable,
  rating,
  isSubmitting = false,
  showSavedIndicator = false,
  isSubmitted = false,
}: RatingModuleProps) => {
  const [selectedRating, setSelectedRating] = useState(rating);


  useEffect(() => {
    setSelectedRating(rating);
  }, [rating]);

  const handleRatingPress = async (rating: number) => {
    if (selectedRating === rating) {
      return;
    }

    const newRating = rating;

    setSelectedRating(newRating);
    onRatingChange(newRating);

    if (onAutoSave) {
      try {
        await onAutoSave(newRating);
      } catch (error) {
        console.error('Error auto-saving rating:', error);
      }
    }
  };

  return (
    <View {...ratingStyles.container}>
         <Text {...ratingStyles.title}>{title}</Text>
         {isSubmitted && (
           <Text {...ratingStyles.successText}>
             ✓ {title} rating submitted successfully!
           </Text>
         )}
      <XStack {...ratingStyles.starsContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <Star
            key={rating}
            size={"$5"}
            marginInlineEnd={15}
            color={"#175CD3"}
            strokeWidth={1}
            fill={selectedRating >= rating ? "#175CD3" : "transparent"}
            onPress={() => handleRatingPress(rating)}
            disabled={!isEditable || isSubmitting}
            opacity={isSubmitting ? 0.6 : 1}
          />
        ))}
      </XStack>
      <View {...ratingStyles.divider}></View>
    </View>
  );
};

export default RatingModule;

const ratingStyles = {
  container: {
    marginBlockStart: 20,
  },

  title: {
    fontSize: 14,
    fontWeight: 500 as any,
    color: "$textcolor" as any,
  },
  successText: {
    fontSize: 14,
    fontWeight: 500 as any,
    color: "#10B981" as any,
    marginBlockStart: 5,
  },
  divider: {
    height: 1,
    backgroundColor: "$primaryBorderColor",
  },
  starsContainer: {
    marginBlock: 15,
  },
};
