import { Platform } from 'react-native';

// Try to import PipAndroid safely
let PipAndroid: any = null;
try {
  PipAndroid = require('react-native-pip-android').default;
} catch (error) {
  console.log('[PiP Service] react-native-pip-android not available:', error);
}

// For iOS, we'll use a JavaScript-only implementation for now
const PipModule = null;

// Enhanced logging utility for PiP debugging
const logPiP = (level: 'INFO' | 'WARN' | 'ERROR', message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[PiP Service ${timestamp}] ${level}: ${message}`;

  if (level === 'ERROR') {
    console.error(logMessage, data || '');
  } else if (level === 'WARN') {
    console.warn(logMessage, data || '');
  } else {
    console.log(logMessage, data || '');
  }
};

// Log initial module availability
logPiP('INFO', `Platform: ${Platform.OS}`);
logPiP('INFO', `PipModule available: ${!!PipModule}`);
logPiP('INFO', `PipAndroid available: ${!!PipAndroid}`);

if (Platform.OS === 'ios') {
  logPiP('INFO', `iOS PiP: Using JavaScript-only implementation`);
} else if (Platform.OS === 'android') {
  logPiP('INFO', `Android PipAndroid methods:`, PipAndroid ? Object.keys(PipAndroid) : 'Module not found');
}

export interface PipConfig {
  width?: number;
  height?: number;
  aspectRatio?: { numerator: number; denominator: number };
}

export interface PipServiceInterface {
  enterPip: (config?: PipConfig) => Promise<boolean>;
  exitPip: () => Promise<boolean>;
  isPipSupported: () => boolean;
  isPipActive: () => boolean;
  addPipListener: (callback: (isInPip: boolean) => void) => void;
  removePipListener: () => void;
}

class PipService implements PipServiceInterface {
  private pipListeners: ((isInPip: boolean) => void)[] = [];
  private isInPipMode = false;

  constructor() {
    logPiP('INFO', 'PipService constructor called');

    if (Platform.OS === 'android') {
      logPiP('INFO', 'Setting up Android PiP listeners');
      if (PipAndroid && PipAndroid.addListener) {
        try {
          PipAndroid.addListener('onPictureInPictureModeChanged', (isInPipMode: boolean) => {
            logPiP('INFO', `Android PiP mode changed: ${isInPipMode}`);
            this.isInPipMode = isInPipMode;
            this.pipListeners.forEach(callback => callback(isInPipMode));
          });
          logPiP('INFO', 'Android PiP listener setup successful');
        } catch (error) {
          logPiP('ERROR', 'Failed to setup Android PiP listener', error);
        }
      } else {
        logPiP('WARN', 'PipAndroid.addListener not available');
      }
    } else if (Platform.OS === 'ios') {
      logPiP('INFO', 'Setting up iOS PiP listeners (JavaScript-only implementation)');
      logPiP('INFO', 'iOS PiP will use simulated events');
    }
  }

  async enterPip(config?: PipConfig): Promise<boolean> {
    logPiP('INFO', 'enterPip called', { config, platform: Platform.OS });

    try {
      if (Platform.OS === 'android') {
        logPiP('INFO', 'Attempting Android PiP entry');
        if (!PipAndroid) {
          logPiP('ERROR', 'PipAndroid module not available');
          return false;
        }

        const aspectRatio = config?.aspectRatio || { numerator: 16, denominator: 9 };
        logPiP('INFO', 'Android PiP config:', { aspectRatio });

        if (PipAndroid.enterPictureInPictureMode) {
          await PipAndroid.enterPictureInPictureMode({ aspectRatio });
          logPiP('INFO', 'Android PiP entry successful');
          return true;
        } else {
          logPiP('ERROR', 'PipAndroid.enterPictureInPictureMode method not available');
          return false;
        }
      } else if (Platform.OS === 'ios') {
        logPiP('INFO', 'Attempting iOS PiP entry');
        logPiP('INFO', 'Using JavaScript-only PiP implementation for iOS');

        // JavaScript-only implementation for testing UI
        logPiP('INFO', 'Simulating PiP entry');
        setTimeout(() => {
          this.isInPipMode = true;
          this.pipListeners.forEach(callback => callback(true));
        }, 500);
        return true;
      }

      logPiP('WARN', 'Platform not supported for PiP');
      return false;
    } catch (error) {
      logPiP('ERROR', 'Error entering PiP mode', error);
      return false;
    }
  }

  async exitPip(): Promise<boolean> {
    logPiP('INFO', 'exitPip called', { platform: Platform.OS });

    try {
      if (Platform.OS === 'android') {
        logPiP('INFO', 'Attempting Android PiP exit');
        if (!PipAndroid || !PipAndroid.exitPictureInPictureMode) {
          logPiP('ERROR', 'PipAndroid.exitPictureInPictureMode not available');
          return false;
        }

        await PipAndroid.exitPictureInPictureMode();
        logPiP('INFO', 'Android PiP exit successful');
        return true;
      } else if (Platform.OS === 'ios') {
        logPiP('INFO', 'Attempting iOS PiP exit');
        logPiP('INFO', 'Using JavaScript-only PiP exit implementation');

        // JavaScript-only implementation for testing UI
        logPiP('INFO', 'Simulating PiP exit');
        setTimeout(() => {
          this.isInPipMode = false;
          this.pipListeners.forEach(callback => callback(false));
        }, 500);
        return true;
      }

      logPiP('WARN', 'Platform not supported for PiP exit');
      return false;
    } catch (error) {
      logPiP('ERROR', 'Error exiting PiP mode', error);
      return false;
    }
  }

  isPipSupported(): boolean {
    logPiP('INFO', 'isPipSupported called', { platform: Platform.OS });

    if (Platform.OS === 'android') {
      const supported = PipAndroid && PipAndroid.isPictureInPictureSupported ? PipAndroid.isPictureInPictureSupported() : false;
      logPiP('INFO', `Android PiP supported: ${supported}`);
      return supported;
    } else if (Platform.OS === 'ios') {
      // Return true for iOS 14+ to test the UI (JavaScript-only implementation)
      const iosVersion = parseFloat(Platform.Version as string);
      const supported = iosVersion >= 14;
      logPiP('INFO', `iOS PiP supported: ${supported} (iOS version: ${Platform.Version}, using JavaScript-only implementation)`);

      return supported;
    }

    logPiP('INFO', 'PiP not supported on this platform');
    return false;
  }

  isPipActive(): boolean {
    return this.isInPipMode;
  }

  addPipListener(callback: (isInPip: boolean) => void): void {
    this.pipListeners.push(callback);
  }

  removePipListener(): void {
    this.pipListeners = [];
  }
}

export default new PipService();
