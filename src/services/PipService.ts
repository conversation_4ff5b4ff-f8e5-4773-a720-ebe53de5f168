import { Platform, Dimensions, NativeModules, NativeEventEmitter } from 'react-native';
import PipAndroid from 'react-native-pip-android';

const { PipModule } = NativeModules;

export interface PipConfig {
  width?: number;
  height?: number;
  aspectRatio?: { numerator: number; denominator: number };
}

export interface PipServiceInterface {
  enterPip: (config?: PipConfig) => Promise<boolean>;
  exitPip: () => Promise<boolean>;
  isPipSupported: () => boolean;
  isPipActive: () => boolean;
  addPipListener: (callback: (isInPip: boolean) => void) => void;
  removePipListener: () => void;
}

class PipService implements PipServiceInterface {
  private pipListeners: ((isInPip: boolean) => void)[] = [];
  private isInPipMode = false;
  private iosEventEmitter?: NativeEventEmitter;

  constructor() {
    if (Platform.OS === 'android') {
      // Listen for PiP mode changes on Android
      PipAndroid.addListener('onPictureInPictureModeChanged', (isInPipMode: boolean) => {
        this.isInPipMode = isInPipMode;
        this.pipListeners.forEach(callback => callback(isInPipMode));
      });
    } else if (Platform.OS === 'ios' && PipModule) {
      // Listen for PiP mode changes on iOS
      this.iosEventEmitter = new NativeEventEmitter(PipModule);
      this.iosEventEmitter.addListener('onPictureInPictureModeChanged', (event: any) => {
        this.isInPipMode = event.isInPictureInPicture;
        this.pipListeners.forEach(callback => callback(event.isInPictureInPicture));
      });
    }
  }

  async enterPip(config?: PipConfig): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const { width, height } = Dimensions.get('window');
        const aspectRatio = config?.aspectRatio || { numerator: 16, denominator: 9 };
        
        await PipAndroid.enterPictureInPictureMode({
          aspectRatio,
        });
        return true;
      } else if (Platform.OS === 'ios' && PipModule) {
        // iOS PiP implementation using native module
        const result = await PipModule.enterPictureInPicture();
        return result;
      }
      return false;
    } catch (error) {
      console.error('Error entering PiP mode:', error);
      return false;
    }
  }

  async exitPip(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        await PipAndroid.exitPictureInPictureMode();
        return true;
      } else if (Platform.OS === 'ios' && PipModule) {
        // iOS PiP exit implementation
        const result = await PipModule.exitPictureInPicture();
        return result;
      }
      return false;
    } catch (error) {
      console.error('Error exiting PiP mode:', error);
      return false;
    }
  }

  isPipSupported(): boolean {
    if (Platform.OS === 'android') {
      return PipAndroid.isPictureInPictureSupported();
    } else if (Platform.OS === 'ios' && PipModule) {
      // iOS PiP support check - this is async but we'll handle it synchronously for now
      // In a real implementation, you might want to cache this value
      return true; // Assume supported if module is available
    }
    return false;
  }

  isPipActive(): boolean {
    return this.isInPipMode;
  }

  addPipListener(callback: (isInPip: boolean) => void): void {
    this.pipListeners.push(callback);
  }

  removePipListener(): void {
    this.pipListeners = [];
  }
}

export default new PipService();
