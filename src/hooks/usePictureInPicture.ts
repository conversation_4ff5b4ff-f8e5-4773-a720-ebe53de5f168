import { useState, useEffect, useCallback } from 'react';
import PipService, { PipConfig } from '../services/PipService';

export interface UsePictureInPictureReturn {
  isInPip: boolean;
  isPipSupported: boolean;
  enterPip: (config?: PipConfig) => Promise<boolean>;
  exitPip: () => Promise<boolean>;
}

export const usePictureInPicture = (): UsePictureInPictureReturn => {
  const [isInPip, setIsInPip] = useState(false);
  const [isPipSupported] = useState(PipService.isPipSupported());

  useEffect(() => {
    // Add listener for PiP mode changes
    const handlePipChange = (inPipMode: boolean) => {
      setIsInPip(inPipMode);
    };

    PipService.addPipListener(handlePipChange);

    // Initialize with current PiP state
    setIsInPip(PipService.isPipActive());

    return () => {
      PipService.removePipListener();
    };
  }, []);

  const enterPip = useCallback(async (config?: PipConfig): Promise<boolean> => {
    if (!isPipSupported) {
      console.warn('Picture-in-Picture is not supported on this device');
      return false;
    }

    try {
      const success = await PipService.enterPip(config);
      if (success) {
        console.log('Successfully entered PiP mode');
      } else {
        console.warn('Failed to enter PiP mode');
      }
      return success;
    } catch (error) {
      console.error('Error entering PiP mode:', error);
      return false;
    }
  }, [isPipSupported]);

  const exitPip = useCallback(async (): Promise<boolean> => {
    try {
      const success = await PipService.exitPip();
      if (success) {
        console.log('Successfully exited PiP mode');
      } else {
        console.warn('Failed to exit PiP mode');
      }
      return success;
    } catch (error) {
      console.error('Error exiting PiP mode:', error);
      return false;
    }
  }, []);

  return {
    isInPip,
    isPipSupported,
    enterPip,
    exitPip,
  };
};
