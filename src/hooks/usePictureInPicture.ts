import { useState, useEffect, useCallback } from 'react';
import PipService, { PipConfig } from '../services/PipService';

// Enhanced logging for hook
const logHook = (level: 'INFO' | 'WARN' | 'ERROR', message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[PiP Hook ${timestamp}] ${level}: ${message}`;

  if (level === 'ERROR') {
    console.error(logMessage, data || '');
  } else if (level === 'WARN') {
    console.warn(logMessage, data || '');
  } else {
    console.log(logMessage, data || '');
  }
};

export interface UsePictureInPictureReturn {
  isInPip: boolean;
  isPipSupported: boolean;
  enterPip: (config?: PipConfig) => Promise<boolean>;
  exitPip: () => Promise<boolean>;
}

export const usePictureInPicture = (): UsePictureInPictureReturn => {
  logHook('INFO', 'usePictureInPicture hook initialized');

  const [isInPip, setIsInPip] = useState(false);
  const [isPipSupported] = useState(() => {
    const supported = PipService.isPipSupported();
    logHook('INFO', `PiP supported: ${supported}`);
    return supported;
  });

  useEffect(() => {
    logHook('INFO', 'Setting up PiP listeners in hook');

    // Add listener for PiP mode changes
    const handlePipChange = (inPipMode: boolean) => {
      logHook('INFO', `PiP mode changed in hook: ${inPipMode}`);
      setIsInPip(inPipMode);
    };

    PipService.addPipListener(handlePipChange);

    // Initialize with current PiP state
    const currentState = PipService.isPipActive();
    logHook('INFO', `Initial PiP state: ${currentState}`);
    setIsInPip(currentState);

    return () => {
      logHook('INFO', 'Cleaning up PiP listeners in hook');
      PipService.removePipListener();
    };
  }, []);

  const enterPip = useCallback(async (config?: PipConfig): Promise<boolean> => {
    logHook('INFO', 'enterPip called from hook', { config, isPipSupported });

    if (!isPipSupported) {
      logHook('WARN', 'Picture-in-Picture is not supported on this device');
      return false;
    }

    try {
      logHook('INFO', 'Calling PipService.enterPip');
      const success = await PipService.enterPip(config);
      logHook('INFO', `PipService.enterPip result: ${success}`);

      if (success) {
        logHook('INFO', 'Successfully entered PiP mode');
      } else {
        logHook('WARN', 'Failed to enter PiP mode');
      }
      return success;
    } catch (error) {
      logHook('ERROR', 'Error entering PiP mode in hook', error);
      return false;
    }
  }, [isPipSupported]);

  const exitPip = useCallback(async (): Promise<boolean> => {
    logHook('INFO', 'exitPip called from hook');

    try {
      logHook('INFO', 'Calling PipService.exitPip');
      const success = await PipService.exitPip();
      logHook('INFO', `PipService.exitPip result: ${success}`);

      if (success) {
        logHook('INFO', 'Successfully exited PiP mode');
      } else {
        logHook('WARN', 'Failed to exit PiP mode');
      }
      return success;
    } catch (error) {
      logHook('ERROR', 'Error exiting PiP mode in hook', error);
      return false;
    }
  }, []);

  return {
    isInPip,
    isPipSupported,
    enterPip,
    exitPip,
  };
};
