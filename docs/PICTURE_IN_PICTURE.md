# Picture-in-Picture (PiP) Implementation

This document describes the Picture-in-Picture functionality implemented for provider video calls in the VitalCare app.

## Overview

The PiP feature allows providers to minimize the video call interface into a small floating window while using other apps or navigating within the VitalCare app. This enhances multitasking capabilities during video consultations.

## Features

- **Cross-platform support**: Works on both iOS and Android
- **Seamless integration**: Integrated with existing Zoom Video SDK calls
- **Optimized UI**: Compact controls and video layout for PiP mode
- **Easy controls**: Simple minimize/maximize buttons in call interface

## Platform Support

### Android
- **Minimum API Level**: 26 (Android 8.0)
- **Library**: `react-native-pip-android`
- **Configuration**: Automatic via expo plugin

### iOS
- **Minimum Version**: iOS 14.0
- **Implementation**: Custom native module using AVPictureInPictureController
- **Background Mode**: `picture-in-picture` added to UIBackgroundModes

## Usage

### For Providers

1. **Start a video call** as usual through the provider interface
2. **Enter PiP mode** by tapping the minimize button (⬇️) in the call controls
3. **Continue using the app** or switch to other apps while the video call continues in PiP
4. **Exit PiP mode** by tapping the maximize button (⬆️) or tapping the PiP window

### Technical Implementation

#### Components Modified

1. **`src/app/provider/call.tsx`**
   - Added PiP controls and state management
   - Conditional rendering for PiP mode
   - Optimized layout for small PiP window

2. **`src/services/PipService.ts`**
   - Cross-platform PiP service abstraction
   - Event handling for PiP state changes

3. **`src/hooks/usePictureInPicture.ts`**
   - React hook for easy PiP integration
   - State management and event listeners

#### Native Modules

1. **iOS**: `ios/VitalCare/PipModule.{h,m}`
   - AVPictureInPictureController integration
   - Event emission for React Native

2. **Android**: Expo plugin configuration
   - Automatic AndroidManifest.xml modification
   - PiP activity configuration

## Configuration Files

### app.json
- Added `picture-in-picture` to iOS UIBackgroundModes
- Added Android PiP plugin

### package.json
- Added `react-native-pip-android` dependency

## Limitations

1. **iOS Video Integration**: Current iOS implementation uses a placeholder player layer. For full video integration, the native module needs to be connected to the actual Zoom video stream.

2. **Network Handling**: PiP mode should maintain network quality monitoring and call stability.

3. **Call Controls**: Limited controls available in PiP mode for optimal space usage.

## Future Enhancements

1. **Full iOS Video Integration**: Connect PiP to actual Zoom video streams
2. **Custom PiP Controls**: Add call-specific controls to PiP interface
3. **Auto-PiP**: Automatically enter PiP when app goes to background during calls
4. **PiP Positioning**: Allow users to customize PiP window position

## Testing

### Android Testing
1. Build the app with `eas build --profile development --platform android`
2. Start a provider call
3. Tap the minimize button to enter PiP
4. Navigate to other apps to verify PiP functionality

### iOS Testing
1. Build the app with `eas build --profile development --platform ios`
2. Ensure iOS 14.0+ device/simulator
3. Start a provider call
4. Test PiP functionality (note: video integration may be limited)

## Troubleshooting

### Android Issues
- **PiP not supported**: Check device API level (requires Android 8.0+)
- **PiP button not showing**: Verify `react-native-pip-android` installation

### iOS Issues
- **PiP not available**: Check iOS version (requires 14.0+)
- **Native module not found**: Verify iOS native module compilation

### General Issues
- **Call quality in PiP**: Monitor network status and Zoom SDK performance
- **UI layout issues**: Check responsive design for different screen sizes
